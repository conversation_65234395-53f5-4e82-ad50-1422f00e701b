/**
 * Site Settings Initialization API
 * 
 * Creates the site_settings table and inserts default values if it doesn't exist.
 * This is a one-time setup endpoint.
 */

import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createAdminClient()
    
    // Create the site_settings table
    const createTableSQL = `
      -- Create site_settings table
      CREATE TABLE IF NOT EXISTS site_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          setting_key VARCHAR(100) UNIQUE NOT NULL,
          setting_value TEXT,
          setting_type VARCHAR(50) NOT NULL DEFAULT 'string',
          cloudinary_public_id VARCHAR(500),
          cloudinary_url VARCHAR(1000),
          description TEXT,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by <PERSON><PERSON><PERSON>,
          updated_by UUID
      );

      -- Create indexes for performance
      CREATE INDEX IF NOT EXISTS idx_site_settings_key ON site_settings(setting_key);
      CREATE INDEX IF NOT EXISTS idx_site_settings_type ON site_settings(setting_type);
      CREATE INDEX IF NOT EXISTS idx_site_settings_active ON site_settings(is_active);

      -- Enable RLS
      ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;

      -- Create RLS policies
      DROP POLICY IF EXISTS "Allow authenticated users to read site settings" ON site_settings;
      CREATE POLICY "Allow authenticated users to read site settings" ON site_settings
          FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

      DROP POLICY IF EXISTS "Allow authenticated users to update site settings" ON site_settings;
      CREATE POLICY "Allow authenticated users to update site settings" ON site_settings
          FOR UPDATE USING (auth.role() = 'authenticated');

      DROP POLICY IF EXISTS "Allow authenticated users to insert site settings" ON site_settings;
      CREATE POLICY "Allow authenticated users to insert site settings" ON site_settings
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

      DROP POLICY IF EXISTS "Allow service role full access to site settings" ON site_settings;
      CREATE POLICY "Allow service role full access to site settings" ON site_settings
          FOR ALL USING (auth.role() = 'service_role');

      -- Grant permissions
      GRANT SELECT, INSERT, UPDATE ON site_settings TO authenticated;
    `

    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL })
    
    if (createError) {
      console.error('Error creating site_settings table:', createError)
      // Try a simpler approach - just create the table directly
      const { error: directError } = await supabase
        .from('site_settings')
        .select('id')
        .limit(1)
      
      if (directError && directError.code === 'PGRST116') {
        return NextResponse.json({ 
          error: 'Database table creation failed. Please run the SQL schema manually.',
          sql: createTableSQL
        }, { status: 500 })
      }
    }

    // Insert default settings
    const defaultSettings = [
      {
        setting_key: 'site_logo',
        setting_value: '/images/logo.png',
        setting_type: 'image',
        description: 'Main site logo displayed in header and sidebar'
      },
      {
        setting_key: 'hero_background',
        setting_value: '/images/lgu-ipil.png',
        setting_type: 'image',
        description: 'Background image for homepage hero section'
      },
      {
        setting_key: 'site_favicon',
        setting_value: null,
        setting_type: 'image',
        description: 'Browser favicon and app icon'
      },
      {
        setting_key: 'site_name',
        setting_value: 'LGU Ipil',
        setting_type: 'string',
        description: 'Site name displayed in headers'
      },
      {
        setting_key: 'site_tagline',
        setting_value: 'Local Gov',
        setting_type: 'string',
        description: 'Site tagline or subtitle'
      },
      {
        setting_key: 'site_description',
        setting_value: 'Municipal Agriculture Office - Ipil | Personnel Management System',
        setting_type: 'string',
        description: 'Site description for meta tags'
      }
    ]

    // Insert default settings (ignore conflicts)
    for (const setting of defaultSettings) {
      const { error: insertError } = await supabase
        .from('site_settings')
        .upsert(setting, { onConflict: 'setting_key' })
      
      if (insertError) {
        console.error(`Error inserting setting ${setting.setting_key}:`, insertError)
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Site settings initialized successfully',
      settings_count: defaultSettings.length
    })

  } catch (error) {
    console.error('Site settings initialization error:', error)
    return NextResponse.json({ 
      error: 'Failed to initialize site settings',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
