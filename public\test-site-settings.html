<!DOCTYPE html>
<html>
<head>
    <title>Test Site Settings API</title>
</head>
<body>
    <h1>Test Site Settings API</h1>
    
    <div>
        <h2>Test POST Endpoint</h2>
        <button onclick="testPost()">Test POST /api/site-settings</button>
        <div id="postResult"></div>
    </div>
    
    <div>
        <h2>Test GET Endpoint</h2>
        <button onclick="testGet()">Test GET /api/site-settings</button>
        <div id="getResult"></div>
    </div>

    <script>
        async function testPost() {
            const resultDiv = document.getElementById('postResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/api/site-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        setting_key: 'test_logo',
                        setting_value: 'https://example.com/test-logo.png',
                        cloudinary_url: 'https://example.com/test-logo.png',
                        cloudinary_public_id: 'test-logo-123',
                        setting_type: 'image'
                    })
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>Response (${response.status}):</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testGet() {
            const resultDiv = document.getElementById('getResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/api/site-settings');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>Response (${response.status}):</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
