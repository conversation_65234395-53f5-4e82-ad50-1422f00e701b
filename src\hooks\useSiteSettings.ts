/**
 * Site Settings Hook
 * 
 * Custom hook for managing site-wide settings including logos and branding assets.
 * Provides functions to load, update, and manage site settings with Cloudinary integration.
 */

import { useState, useEffect, useCallback } from 'react'

export interface SiteSetting {
  value: string | null
  cloudinary_url: string | null
  cloudinary_public_id: string | null
  type: string
  description: string
}

export interface SiteSettings {
  site_logo?: SiteSetting
  hero_background?: SiteSetting
  site_favicon?: SiteSetting
  site_name?: SiteSetting
  site_tagline?: SiteSetting
  site_description?: SiteSetting
}

export interface UseSiteSettingsReturn {
  settings: SiteSettings
  loading: boolean
  error: string | null
  loadSettings: () => Promise<void>
  updateSetting: (key: string, value: string, cloudinaryUrl?: string, cloudinaryPublicId?: string) => Promise<boolean>
  updateMultipleSettings: (settings: Record<string, Partial<SiteSetting>>) => Promise<boolean>
  getSetting: (key: string, fallback?: string) => string | null
  getSettingUrl: (key: string, fallback?: string) => string | null
}

export function useSiteSettings(): UseSiteSettingsReturn {
  const [settings, setSettings] = useState<SiteSettings>({
    site_logo: {
      value: '/images/logo.png',
      cloudinary_url: null,
      cloudinary_public_id: null,
      type: 'image',
      description: 'Main site logo'
    },
    hero_background: {
      value: '/images/lgu-ipil.png',
      cloudinary_url: null,
      cloudinary_public_id: null,
      type: 'image',
      description: 'Hero background image'
    },
    site_name: {
      value: 'LGU Ipil',
      cloudinary_url: null,
      cloudinary_public_id: null,
      type: 'string',
      description: 'Site name'
    },
    site_tagline: {
      value: 'Local Gov',
      cloudinary_url: null,
      cloudinary_public_id: null,
      type: 'string',
      description: 'Site tagline'
    }
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadSettings = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/site-settings')
      if (!response.ok) {
        console.warn('Failed to load site settings from API, using defaults')
        // Don't throw error, just use default settings
        return
      }

      const data = await response.json()
      if (data.settings) {
        setSettings(prev => ({ ...prev, ...data.settings }))
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load settings'
      console.warn('Error loading site settings, using defaults:', errorMessage)
      // Don't set error state, just use defaults
    } finally {
      setLoading(false)
    }
  }, [])

  const updateSetting = useCallback(async (
    key: string, 
    value: string, 
    cloudinaryUrl?: string, 
    cloudinaryPublicId?: string
  ): Promise<boolean> => {
    try {
      const response = await fetch('/api/site-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          setting_key: key,
          setting_value: value,
          cloudinary_url: cloudinaryUrl,
          cloudinary_public_id: cloudinaryPublicId
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || `Failed to update setting (${response.status})`
        console.error('Site settings API error:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          url: response.url
        })
        throw new Error(errorMessage)
      }

      // Update local state
      setSettings(prev => ({
        ...prev,
        [key]: {
          ...prev[key],
          value,
          cloudinary_url: cloudinaryUrl || prev[key]?.cloudinary_url || null,
          cloudinary_public_id: cloudinaryPublicId || prev[key]?.cloudinary_public_id || null
        }
      }))

      return true

    } catch (err) {
      console.error('Error updating setting:', err)
      setError(err instanceof Error ? err.message : 'Failed to update setting')
      return false
    }
  }, [])

  const updateMultipleSettings = useCallback(async (
    settingsToUpdate: Record<string, Partial<SiteSetting>>
  ): Promise<boolean> => {
    try {
      const response = await fetch('/api/site-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: settingsToUpdate
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update settings')
      }

      // Update local state
      setSettings(prev => {
        const updated = { ...prev }
        Object.entries(settingsToUpdate).forEach(([key, value]) => {
          updated[key] = {
            ...prev[key],
            ...value
          }
        })
        return updated
      })

      return true

    } catch (err) {
      console.error('Error updating multiple settings:', err)
      setError(err instanceof Error ? err.message : 'Failed to update settings')
      return false
    }
  }, [])

  const getSetting = useCallback((key: string, fallback?: string): string | null => {
    const setting = settings[key as keyof SiteSettings]
    return setting?.value || fallback || null
  }, [settings])

  const getSettingUrl = useCallback((key: string, fallback?: string): string | null => {
    const setting = settings[key as keyof SiteSettings]
    return setting?.cloudinary_url || setting?.value || fallback || null
  }, [settings])

  // Load settings on mount
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  return {
    settings,
    loading,
    error,
    loadSettings,
    updateSetting,
    updateMultipleSettings,
    getSetting,
    getSettingUrl
  }
}
